// This file configures the initialization of Sentry on the client.
// The added config here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import { getStudentUserInfo } from "@repo/core/utils/stu/device";
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: "https://<EMAIL>/2",

  beforeSend(event, hint) {
    // 过滤掉 base64 编码的附件
    if (hint.attachments) {
      hint.attachments = hint.attachments.map((attachment) => {
        // 对附件进行裁剪，或移除 base64 数据
        if (
          attachment.data &&
          typeof attachment.data === "string" &&
          attachment.data.startsWith?.("data:")
        ) {
          // 移除 base64 数据或进行处理
          attachment.data = ""; // 这里将数据清空，或者你也可以选择裁剪
        }
        return attachment;
      });
    }

    // 如果有堆栈信息，裁剪它
    if (event.exception && event.exception.values) {
      event.exception.values = event.exception.values.map((exception) => {
        // 你可以裁剪异常的堆栈信息，或者去掉一些不必要的字段
        if (exception.stacktrace) {
          exception.stacktrace.frames = exception.stacktrace.frames?.slice(
            0,
            20
          ); // 保留最多 20 个栈帧
        }
        return exception;
      });
    }

    // 你可以根据需要裁剪消息内容
    if (event.message && event.message.length > 500) {
      event.message = event.message.substring(0, 500); // 限制消息的最大长度为 500 字符
    }
    // 返回修改后的事件
    return event;
  },

  // Add optional integrations for additional features
  integrations: [
    Sentry.globalHandlersIntegration(),
    Sentry.browserTracingIntegration(),
    Sentry.browserProfilingIntegration(),
    Sentry.replayIntegration({
      maskAllInputs: false,
      maskAllText: false,
    }),
  ],
  tracePropagationTargets: [/^https:\/\/(.*)(xiaoluxue\.cn|xiaoluxue\.com)/],
  profileSessionSampleRate: 0.1,
  profilesSampleRate: 1.0,
  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
  tracesSampleRate: 1.0,
  // Enable logs to be sent to Sentry
  enableLogs: true,

  // Define how likely Replay events are sampled.
  // This sets the sample rate to be 10%. You may want this to be 100% while
  // in development and sample at a lower rate in production
  replaysSessionSampleRate: 0.1,

  // Define how likely Replay events are sampled when an error occurs.
  replaysOnErrorSampleRate: 1.0,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,
});

const user = getStudentUserInfo();
if (user) {
  const { userId: id, userName: username, ...rest } = user;
  Sentry.setUser({
    id,
    username,
    ...rest,
  });
}

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
